import { type NextRequest, NextResponse } from "next/server"

// Waste categories for CLIP classification
const WASTE_CATEGORIES = [
  { id: "plastic_bottle", label: "plastic bottle", description: "a plastic water or soda bottle" },
  { id: "glass_bottle", label: "glass bottle", description: "a glass bottle or jar" },
  { id: "aluminum_can", label: "aluminum can", description: "an aluminum soda or beer can" },
  { id: "paper_waste", label: "paper waste", description: "paper, newspaper, or cardboard" },
  { id: "cardboard", label: "cardboard", description: "cardboard box or packaging" },
  { id: "plastic_bag", label: "plastic bag", description: "a plastic shopping or garbage bag" },
  { id: "food_waste", label: "food waste", description: "leftover food or organic waste" },
  { id: "electronic_waste", label: "electronic waste", description: "electronic device, phone, or battery" },
  { id: "textile_waste", label: "textile waste", description: "clothing, fabric, or textile material" },
  { id: "metal_scrap", label: "metal scrap", description: "metal objects or scrap metal" },
  { id: "hazardous_waste", label: "hazardous waste", description: "chemical containers or hazardous materials" },
  { id: "general_trash", label: "general trash", description: "mixed waste or unidentifiable garbage" }
]

// Working zero-shot classification for waste detection
async function detectWasteWithCLIP(base64Image: string) {
  try {
    console.log("🤖 Using working zero-shot classification...")

    // Step 1: Extract text labels for API (jaise tumne bataya tha)
    const textLabelsForAPI = WASTE_CATEGORIES.map(category => category.description)

    console.log("📋 Sending candidates array:", textLabelsForAPI)

    // Step 2: Try working zero-shot classification model
    const response = await fetch(
      "https://api-inference.huggingface.co/models/facebook/bart-large-mnli",
      {
        headers: {
          Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({
          inputs: `This image shows ${textLabelsForAPI.join(' or ')}`,
          parameters: {
            candidate_labels: textLabelsForAPI
          }
        }),
      }
    )

    console.log("API Response status:", response.status)

    if (!response.ok) {
      // If this fails, try the garbage classification model that was working before
      console.log("❌ Zero-shot failed, trying garbage classification...")

      const garbageResponse = await fetch(
        "https://api-inference.huggingface.co/models/yangy50/garbage-classification",
        {
          headers: {
            Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: base64Image
          }),
        }
      )

      if (garbageResponse.ok) {
        const garbageResult = await garbageResponse.json()
        console.log("✅ Garbage classification success:", garbageResult)

        if (Array.isArray(garbageResult)) {
          const mappedResults = garbageResult.map((item: any) => {
            // Map garbage classification labels to our waste categories
            const wasteTypeMap: { [key: string]: string } = {
              'paper': 'paper_waste',
              'plastic': 'plastic_bottle',
              'metal': 'metal_scrap',
              'glass': 'glass_bottle',
              'cardboard': 'cardboard',
              'trash': 'general_trash'
            }

            const wasteId = wasteTypeMap[item.label.toLowerCase()] || 'general_trash'
            const wasteCategory = WASTE_CATEGORIES.find(cat => cat.id === wasteId) || WASTE_CATEGORIES[11]

            return {
              ...item,
              waste_id: wasteId,
              waste_label: wasteCategory.label,
              original_description: wasteCategory.description
            }
          })

          return {
            success: true,
            api_response: mappedResults,
            source: "garbage_classification_fallback",
            best_match: mappedResults[0]
          }
        }
      }

      return {
        success: false,
        error: `API Error: ${response.status}`,
        message: "Both models failed"
      }
    }

    const result = await response.json()
    console.log("🔍 Zero-shot API Response:", JSON.stringify(result, null, 2))

    // Step 3: Map results back to original objects (jaise tumne explain kiya)
    if (Array.isArray(result)) {
      const mappedResults = result.map((item: any) => {
        // Find the index of this label in our textLabelsForAPI array
        const labelIndex = textLabelsForAPI.findIndex(label => label === item.label)

        if (labelIndex !== -1) {
          // Get the corresponding waste category object
          const wasteCategory = WASTE_CATEGORIES[labelIndex]
          return {
            ...item,
            waste_id: wasteCategory.id,
            waste_label: wasteCategory.label,
            original_description: wasteCategory.description
          }
        }

        return item
      })

      return {
        success: true,
        api_response: mappedResults,
        source: "zero_shot_classification",
        best_match: mappedResults[0] // Highest confidence result
      }
    }

    // Fallback response format
    return {
      success: true,
      api_response: result,
      source: "zero_shot_classification"
    }

  } catch (error) {
    console.error("Classification API call error:", error)
    return {
      success: false,
      error: "Classification API call failed",
      message: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

// Main API handler
export async function POST(request: NextRequest) {
  try {
    console.log("=== Waste Detection API Called ===")
    
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json({ 
        success: false, 
        error: "No image file provided" 
      }, { status: 400 })
    }

    console.log(`Image received: ${file.name} Size: ${file.size} Type: ${file.type}`)

    // Convert image to base64
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64Image = buffer.toString('base64')
    
    console.log(`Image converted to base64, length: ${base64Image.length}`)

    // Call OpenAI CLIP for waste detection
    const result = await detectWasteWithCLIP(base64Image)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to process image",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    }, { status: 500 })
  }
}
