import { type NextRequest, NextResponse } from "next/server"

// Waste categories for CLIP classification
const WASTE_CATEGORIES = [
  { id: "plastic_bottle", label: "plastic bottle", description: "a plastic water or soda bottle" },
  { id: "glass_bottle", label: "glass bottle", description: "a glass bottle or jar" },
  { id: "aluminum_can", label: "aluminum can", description: "an aluminum soda or beer can" },
  { id: "paper_waste", label: "paper waste", description: "paper, newspaper, or cardboard" },
  { id: "cardboard", label: "cardboard", description: "cardboard box or packaging" },
  { id: "plastic_bag", label: "plastic bag", description: "a plastic shopping or garbage bag" },
  { id: "food_waste", label: "food waste", description: "leftover food or organic waste" },
  { id: "electronic_waste", label: "electronic waste", description: "electronic device, phone, or battery" },
  { id: "textile_waste", label: "textile waste", description: "clothing, fabric, or textile material" },
  { id: "metal_scrap", label: "metal scrap", description: "metal objects or scrap metal" },
  { id: "hazardous_waste", label: "hazardous waste", description: "chemical containers or hazardous materials" },
  { id: "general_trash", label: "general trash", description: "mixed waste or unidentifiable garbage" }
]

// CLIP-based waste detection using Hugging Face
async function detectWasteWithCLIP(base64Image: string) {
  try {
    console.log("🤖 CLIP API call to Hugging Face...")

    // Step 1: Extract text labels for API (jaise tumne bataya tha)
    const textLabelsForAPI = WASTE_CATEGORIES.map(category => category.description)

    console.log("📝 Text labels for API:", textLabelsForAPI)

    // Step 2: CLIP API call with image and text labels
    const response = await fetch(
      "https://api-inference.huggingface.co/models/openai/clip-vit-large-patch14",
      {
        headers: {
          Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({
          inputs: {
            image: base64Image,
            candidates: textLabelsForAPI
          }
        }),
      }
    )

    console.log("API Response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("API Error Response:", errorText)
      return {
        success: false,
        error: `API Error: ${response.status}`,
        message: errorText
      }
    }

    const result = await response.json()
    console.log("🔍 CLIP API Response:", JSON.stringify(result, null, 2))

    // Step 3: Map results back to original objects (jaise tumne explain kiya)
    if (Array.isArray(result)) {
      const mappedResults = result.map((item: any) => {
        // Find the index of this label in our textLabelsForAPI array
        const labelIndex = textLabelsForAPI.findIndex(label => label === item.label)

        if (labelIndex !== -1) {
          // Get the corresponding waste category object
          const wasteCategory = WASTE_CATEGORIES[labelIndex]
          return {
            ...item,
            waste_id: wasteCategory.id,
            waste_label: wasteCategory.label,
            original_description: wasteCategory.description
          }
        }

        return item
      })

      return {
        success: true,
        api_response: mappedResults,
        source: "huggingface_clip",
        best_match: mappedResults[0] // Highest confidence result
      }
    }

    // Fallback response format
    return {
      success: true,
      api_response: result,
      source: "huggingface_clip"
    }

  } catch (error) {
    console.error("CLIP API call error:", error)
    return {
      success: false,
      error: "CLIP API call failed",
      message: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

// Main API handler
export async function POST(request: NextRequest) {
  try {
    console.log("=== Waste Detection API Called ===")
    
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json({ 
        success: false, 
        error: "No image file provided" 
      }, { status: 400 })
    }

    console.log(`Image received: ${file.name} Size: ${file.size} Type: ${file.type}`)

    // Convert image to base64
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64Image = buffer.toString('base64')
    
    console.log(`Image converted to base64, length: ${base64Image.length}`)

    // Call CLIP API for waste detection
    const result = await detectWasteWithCLIP(base64Image)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to process image",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    }, { status: 500 })
  }
}
