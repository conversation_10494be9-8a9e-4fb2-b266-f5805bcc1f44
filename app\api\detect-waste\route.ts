import { type NextRequest, NextResponse } from "next/server"

// Direct API call - jo response aaye wahi return karo
async function detectWasteWithHuggingFace(base64Image: string) {
  try {
    console.log("🤖 Direct API call to Hugging Face...")

    // Seedha API call with proper format
    const response = await fetch(
      "https://api-inference.huggingface.co/models/google/vit-base-patch16-224",
      {
        headers: {
          Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          "Content-Type": "application/json",
        },
        method: "POST",
        body: JSON.stringify({
          inputs: base64Image,
          parameters: {}
        }),
      }
    )

    console.log("API Response status:", response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error("API Error Response:", errorText)
      return {
        success: false,
        error: `API Error: ${response.status}`,
        message: errorText
      }
    }

    const result = await response.json()
    console.log("🔍 Direct API Response:", JSON.stringify(result, null, 2))

    // Seedha API ka response return karo
    return {
      success: true,
      api_response: result,
      source: "huggingface_direct"
    }

  } catch (error) {
    console.error("API call error:", error)
    return {
      success: false,
      error: "API call failed",
      message: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

// Main API handler
export async function POST(request: NextRequest) {
  try {
    console.log("=== Waste Detection API Called ===")
    
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json({ 
        success: false, 
        error: "No image file provided" 
      }, { status: 400 })
    }

    console.log(`Image received: ${file.name} Size: ${file.size} Type: ${file.type}`)

    // Convert image to base64
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64Image = buffer.toString('base64')
    
    console.log(`Image converted to base64, length: ${base64Image.length}`)

    // Call Hugging Face API for waste detection
    const result = await detectWasteWithHuggingFace(base64Image)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to process image",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    }, { status: 500 })
  }
}
