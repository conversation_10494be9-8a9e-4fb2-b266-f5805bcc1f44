import { type NextRequest, NextResponse } from "next/server"

// Waste categories for CLIP classification
const WASTE_CATEGORIES = [
  { id: "plastic_bottle", label: "plastic bottle", description: "a plastic water or soda bottle" },
  { id: "glass_bottle", label: "glass bottle", description: "a glass bottle or jar" },
  { id: "aluminum_can", label: "aluminum can", description: "an aluminum soda or beer can" },
  { id: "paper_waste", label: "paper waste", description: "paper, newspaper, or cardboard" },
  { id: "cardboard", label: "cardboard", description: "cardboard box or packaging" },
  { id: "plastic_bag", label: "plastic bag", description: "a plastic shopping or garbage bag" },
  { id: "food_waste", label: "food waste", description: "leftover food or organic waste" },
  { id: "electronic_waste", label: "electronic waste", description: "electronic device, phone, or battery" },
  { id: "textile_waste", label: "textile waste", description: "clothing, fabric, or textile material" },
  { id: "metal_scrap", label: "metal scrap", description: "metal objects or scrap metal" },
  { id: "hazardous_waste", label: "hazardous waste", description: "chemical containers or hazardous materials" },
  { id: "general_trash", label: "general trash", description: "mixed waste or unidentifiable garbage" }
]

// Multiple fallback models for waste detection
async function detectWasteWithMultipleModels(base64Image: string) {
  try {
    console.log("🤖 Starting waste detection with multiple models...")

    // Model 1: Try zero-shot image classification with proper format
    try {
      console.log("🎯 Trying zero-shot image classification...")
      const textLabelsForAPI = WASTE_CATEGORIES.map(category => category.label)

      console.log("📋 Sending candidates array:", textLabelsForAPI)

      const clipResponse = await fetch(
        "https://api-inference.huggingface.co/models/Salesforce/blip-image-captioning-base",
        {
          headers: {
            Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: base64Image
          }),
        }
      )

      if (clipResponse.ok) {
        const result = await clipResponse.json()
        console.log("✅ Image captioning result:", result)

        // Try to match caption with waste categories
        if (result && result[0] && result[0].generated_text) {
          const caption = result[0].generated_text.toLowerCase()
          console.log("📝 Generated caption:", caption)

          // Find best matching waste category
          let bestMatch = WASTE_CATEGORIES[11] // default to general_trash
          let bestScore = 0.1

          WASTE_CATEGORIES.forEach(category => {
            const keywords = category.label.toLowerCase().split(' ')
            let score = 0
            keywords.forEach(keyword => {
              if (caption.includes(keyword)) {
                score += 0.3
              }
            })
            if (score > bestScore) {
              bestScore = score
              bestMatch = category
            }
          })

          return {
            success: true,
            api_response: [{
              label: bestMatch.description,
              score: bestScore,
              waste_id: bestMatch.id,
              waste_label: bestMatch.label,
              original_description: bestMatch.description,
              caption: caption
            }],
            source: "image_captioning_match",
            best_match: {
              label: bestMatch.description,
              score: bestScore,
              waste_id: bestMatch.id,
              waste_label: bestMatch.label
            }
          }
        }
      }
    } catch (captionError) {
      console.log("❌ Image captioning failed, trying next model...")
    }

    // Model 2: Try proper zero-shot classification
    try {
      console.log("🎯 Trying zero-shot classification with CLIP...")
      const textLabelsForAPI = WASTE_CATEGORIES.map(category => category.label)

      const clipResponse = await fetch(
        "https://api-inference.huggingface.co/models/laion/CLIP-ViT-B-32-laion2B-s34B-b79K",
        {
          headers: {
            Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: {
              image: base64Image,
              candidates: textLabelsForAPI
            }
          }),
        }
      )

      if (clipResponse.ok) {
        const result = await clipResponse.json()
        console.log("✅ CLIP Success:", result)

        if (Array.isArray(result)) {
          const mappedResults = result.map((item: any) => {
            const labelIndex = textLabelsForAPI.findIndex(label => label === item.label)
            if (labelIndex !== -1) {
              const wasteCategory = WASTE_CATEGORIES[labelIndex]
              return {
                ...item,
                waste_id: wasteCategory.id,
                waste_label: wasteCategory.label,
                original_description: wasteCategory.description
              }
            }
            return item
          })

          return {
            success: true,
            api_response: mappedResults,
            source: "clip_zero_shot",
            best_match: mappedResults[0]
          }
        }
      }
    } catch (clipError) {
      console.log("❌ CLIP failed, trying next model...")
    }

    // Model 2: Try garbage classification model
    try {
      console.log("🗑️ Trying garbage classification model...")
      const garbageResponse = await fetch(
        "https://api-inference.huggingface.co/models/yangy50/garbage-classification",
        {
          headers: {
            Authorization: `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
            "Content-Type": "application/json",
          },
          method: "POST",
          body: JSON.stringify({
            inputs: base64Image
          }),
        }
      )

      if (garbageResponse.ok) {
        const result = await garbageResponse.json()
        console.log("✅ Garbage classification success:", result)

        if (Array.isArray(result)) {
          const mappedResults = result.map((item: any) => {
            // Map garbage classification labels to our waste categories
            const wasteTypeMap: { [key: string]: string } = {
              'paper': 'paper_waste',
              'plastic': 'plastic_bottle',
              'metal': 'metal_scrap',
              'glass': 'glass_bottle',
              'cardboard': 'cardboard',
              'trash': 'general_trash'
            }

            const wasteId = wasteTypeMap[item.label.toLowerCase()] || 'general_trash'
            const wasteCategory = WASTE_CATEGORIES.find(cat => cat.id === wasteId) || WASTE_CATEGORIES[11]

            return {
              ...item,
              waste_id: wasteId,
              waste_label: wasteCategory.label,
              original_description: wasteCategory.description
            }
          })

          return {
            success: true,
            api_response: mappedResults,
            source: "garbage_classification",
            best_match: mappedResults[0]
          }
        }
      }
    } catch (garbageError) {
      console.log("❌ Garbage classification failed, using fallback...")
    }

    // Fallback: Return mock data
    return {
      success: false,
      error: "All models failed",
      message: "Using fallback detection"
    }

  } catch (error) {
    console.error("All models failed:", error)
    return {
      success: false,
      error: "Detection failed",
      message: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

// Main API handler
export async function POST(request: NextRequest) {
  try {
    console.log("=== Waste Detection API Called ===")
    
    const formData = await request.formData()
    const file = formData.get('image') as File
    
    if (!file) {
      return NextResponse.json({ 
        success: false, 
        error: "No image file provided" 
      }, { status: 400 })
    }

    console.log(`Image received: ${file.name} Size: ${file.size} Type: ${file.type}`)

    // Convert image to base64
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const base64Image = buffer.toString('base64')
    
    console.log(`Image converted to base64, length: ${base64Image.length}`)

    // Call multiple models for waste detection
    const result = await detectWasteWithMultipleModels(base64Image)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error("API Error:", error)
    return NextResponse.json({
      success: false,
      error: "Failed to process image",
      message: error instanceof Error ? error.message : "Unknown error occurred"
    }, { status: 500 })
  }
}
